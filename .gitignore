# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# IDE and editors
.idea/
.vscode/

# Dependencies
node_modules/
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# Testing
/coverage

# Next.js
/.next/
/out/
next-env.d.ts
*.tsbuildinfo

# Production builds
/build
dist/
dist

# Environment files (comprehensive coverage)

*token.json*
*credentials.json*
.env

# Logs and debug files
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
dump.rdb

# System files
.DS_Store
*.pem

# Python
__pycache__/
*pyc*
venv/
.venv/

# Development tools
chainlit.md
.chainlit
.ipynb_checkpoints/
.ac

# Deployment
.vercel

# Data and databases
agenthub/agents/youtube/db

# Archive files and large assets
**/*.zip
**/*.tar.gz
**/*.tar
**/*.tgz
*.pack
*.deb
*.dylib

# Build caches
.cache/

# Mobile development
android-sdk/ frontend/node_modules/.cache/default-development/0.pack
frontend/node_modules/.cache/default-development/0.pack
