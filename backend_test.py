import requests
import sys
import json
from datetime import datetime
import time

class ForexSentimentAPITester:
    def __init__(self, base_url="https://9a133177-547b-4f99-bd3e-30008d02b8ac.preview.emergentagent.com"):
        self.base_url = base_url
        self.tests_run = 0
        self.tests_passed = 0

    def run_test(self, name, method, endpoint, expected_status, data=None, timeout=30):
        """Run a single API test"""
        url = f"{self.base_url}/{endpoint}"
        headers = {'Content-Type': 'application/json'}

        self.tests_run += 1
        print(f"\n🔍 Testing {name}...")
        print(f"   URL: {url}")
        
        try:
            if method == 'GET':
                response = requests.get(url, headers=headers, timeout=timeout)
            elif method == 'POST':
                response = requests.post(url, json=data, headers=headers, timeout=timeout)

            success = response.status_code == expected_status
            if success:
                self.tests_passed += 1
                print(f"✅ Passed - Status: {response.status_code}")
                try:
                    response_data = response.json()
                    print(f"   Response keys: {list(response_data.keys()) if isinstance(response_data, dict) else 'Non-dict response'}")
                    return True, response_data
                except:
                    return True, response.text
            else:
                print(f"❌ Failed - Expected {expected_status}, got {response.status_code}")
                print(f"   Response: {response.text[:200]}...")
                return False, {}

        except requests.exceptions.Timeout:
            print(f"❌ Failed - Request timed out after {timeout} seconds")
            return False, {}
        except Exception as e:
            print(f"❌ Failed - Error: {str(e)}")
            return False, {}

    def test_root_endpoint(self):
        """Test the root API endpoint"""
        return self.run_test(
            "Root API Endpoint",
            "GET",
            "api/",
            200
        )

    def test_api_status(self):
        """Test API status endpoint"""
        success, response = self.run_test(
            "API Status Check",
            "GET",
            "api/status",
            200
        )
        
        if success and isinstance(response, dict):
            print(f"   API Status Details:")
            for service, status in response.items():
                status_icon = "✅" if status == "connected" else "❌"
                print(f"     {status_icon} {service}: {status}")
        
        return success, response

    def test_sentiment_analysis(self):
        """Test sentiment analysis endpoint - this is the main feature"""
        print(f"\n🚀 Testing Main Feature: Sentiment Analysis")
        print(f"   This may take 30-60 seconds as it fetches live news and runs AI analysis...")
        
        success, response = self.run_test(
            "Sentiment Analysis (Main Feature)",
            "POST",
            "api/analyze",
            200,
            timeout=90  # Longer timeout for AI analysis
        )
        
        if success and isinstance(response, dict):
            print(f"   Analysis Results:")
            if 'sentiment_analysis' in response:
                analysis = response['sentiment_analysis']
                print(f"     Overall Sentiment: {analysis.get('overall_sentiment', 'N/A')}")
                print(f"     Bullish Score: {analysis.get('bullish_score', 'N/A')}/10")
                print(f"     Bearish Score: {analysis.get('bearish_score', 'N/A')}/10")
                print(f"     Confidence: {analysis.get('confidence_score', 'N/A')}")
                print(f"     News Count: {analysis.get('news_count', 'N/A')}")
                print(f"     Summary: {analysis.get('analysis_summary', 'N/A')[:100]}...")
            
            if 'articles' in response:
                print(f"     Articles Retrieved: {len(response['articles'])}")
        
        return success, response

    def test_sentiment_history(self):
        """Test sentiment history endpoint"""
        success, response = self.run_test(
            "Sentiment History",
            "GET",
            "api/sentiment-history",
            200
        )
        
        if success and isinstance(response, dict) and 'analyses' in response:
            analyses_count = len(response['analyses'])
            print(f"   Historical analyses found: {analyses_count}")
            if analyses_count > 0:
                latest = response['analyses'][-1]
                print(f"   Latest analysis: {latest.get('overall_sentiment', 'N/A')} at {latest.get('timestamp', 'N/A')}")
        
        return success, response

    def test_latest_news(self):
        """Test latest news endpoint"""
        success, response = self.run_test(
            "Latest News Articles",
            "GET",
            "api/latest-news",
            200
        )
        
        if success and isinstance(response, dict) and 'articles' in response:
            articles_count = len(response['articles'])
            print(f"   News articles found: {articles_count}")
            if articles_count > 0:
                latest = response['articles'][0]
                print(f"   Latest article: {latest.get('title', 'N/A')[:50]}...")
                print(f"   Source: {latest.get('source', 'N/A')}")
                print(f"   Sentiment: {latest.get('sentiment', 'N/A')}")
        
        return success, response

    def test_invalid_endpoint(self):
        """Test invalid endpoint to check error handling"""
        success, response = self.run_test(
            "Invalid Endpoint (Error Handling)",
            "GET",
            "api/nonexistent",
            404
        )
        return success, response

def main():
    print("🚀 Starting Forex Sentiment Analysis API Tests")
    print("=" * 60)
    
    # Setup
    tester = ForexSentimentAPITester()
    
    # Test sequence
    tests = [
        ("Root Endpoint", tester.test_root_endpoint),
        ("API Status", tester.test_api_status),
        ("Sentiment History", tester.test_sentiment_history),
        ("Latest News", tester.test_latest_news),
        ("Sentiment Analysis (Main Feature)", tester.test_sentiment_analysis),
        ("Error Handling", tester.test_invalid_endpoint),
    ]
    
    # Run all tests
    for test_name, test_func in tests:
        try:
            test_func()
        except Exception as e:
            print(f"❌ Test '{test_name}' crashed: {str(e)}")
        
        # Small delay between tests
        time.sleep(1)
    
    # Print final results
    print("\n" + "=" * 60)
    print(f"📊 FINAL RESULTS")
    print(f"Tests passed: {tester.tests_passed}/{tester.tests_run}")
    
    if tester.tests_passed == tester.tests_run:
        print("🎉 All tests passed! Backend API is working correctly.")
        return 0
    else:
        print(f"⚠️  {tester.tests_run - tester.tests_passed} test(s) failed.")
        return 1

if __name__ == "__main__":
    sys.exit(main())