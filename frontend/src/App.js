import React, { useState, useEffect } from 'react';
import axios from 'axios';
import {
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  <PERSON>A<PERSON>s,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer
} from 'recharts';
import { RefreshCw, ExternalLink, Activity } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './components/ui/card';
import { Button } from './components/ui/button';
import { Badge } from './components/ui/badge';
import './App.css';

const API_BASE_URL = process.env.REACT_APP_BACKEND_URL;

// Circular Progress Component for Probability Gauges
const CircularGauge = ({ value, maxValue = 10, color, label }) => {
  const percentage = (value / maxValue) * 100;
  const radius = 45;
  const strokeWidth = 8;
  const normalizedRadius = radius - strokeWidth * 2;
  const circumference = normalizedRadius * 2 * Math.PI;
  const strokeDasharray = `${percentage * circumference / 100} ${circumference}`;

  return (
    <div className="flex flex-col items-center">
      <div className="relative">
        <svg
          height={radius * 2}
          width={radius * 2}
          className="transform -rotate-90"
        >
          <circle
            stroke="#e5e7eb"
            fill="transparent"
            strokeWidth={strokeWidth}
            r={normalizedRadius}
            cx={radius}
            cy={radius}
          />
          <circle
            stroke={color}
            fill="transparent"
            strokeWidth={strokeWidth}
            strokeDasharray={strokeDasharray}
            strokeLinecap="round"
            r={normalizedRadius}
            cx={radius}
            cy={radius}
            className="transition-all duration-700 ease-in-out"
          />
        </svg>
        <div className="absolute inset-0 flex items-center justify-center">
          <span className="text-2xl font-bold" style={{ color }}>
            {value}/10
          </span>
        </div>
      </div>
      <div className="mt-2 text-center">
        <div className="text-sm font-medium" style={{ color }}>
          {(value * 10).toFixed(0)}% Probability
        </div>
      </div>
    </div>
  );
};

// Horizontal Sentiment Gauge Component
const SentimentGauge = ({ sentiment, confidence }) => {
  const getSentimentScore = () => {
    switch (sentiment) {
      case 'bullish': return 75;
      case 'bearish': return -75;
      default: return 0;
    }
  };

  const score = getSentimentScore();
  const position = ((score + 100) / 200) * 100; // Convert -100 to 100 range to 0-100%

  return (
    <div className="w-full">
      <div className="flex justify-between items-center mb-2">
        <span className="text-sm font-medium text-red-600">Bearish (-100)</span>
        <span className="text-sm font-medium text-gray-600">Neutral (0)</span>
        <span className="text-sm font-medium text-green-600">Bullish (+100)</span>
      </div>
      <div className="relative h-6 bg-gray-200 rounded-full">
        {/* Background gradient */}
        <div className="absolute inset-0 bg-gradient-to-r from-red-400 via-yellow-400 to-green-400 rounded-full opacity-30"></div>
        
        {/* Sentiment indicator */}
        <div 
          className="absolute top-0 h-6 w-8 bg-orange-500 rounded-full flex items-center justify-center transform -translate-x-4 transition-all duration-700"
          style={{ left: `${position}%` }}
        >
          <div className="w-2 h-2 bg-white rounded-full"></div>
        </div>
      </div>
      <div className="mt-2 flex items-center justify-center">
        <span className="text-lg font-bold text-orange-600">{score}</span>
        <span className="ml-2 text-sm text-gray-600">
          {sentiment === 'neutral' ? 'Neutral' : sentiment === 'bullish' ? 'Bullish' : 'Bearish'}
        </span>
      </div>
    </div>
  );
};

// News Article Component
const NewsArticle = ({ article, index }) => {
  const getImpactLevel = (score) => {
    if (score > 0.7) return { label: 'high impact', color: 'text-red-600 bg-red-50' };
    if (score > 0.5) return { label: 'medium impact', color: 'text-orange-600 bg-orange-50' };
    return { label: 'low impact', color: 'text-gray-600 bg-gray-50' };
  };

  const getSentimentIcon = (sentiment) => {
    switch (sentiment) {
      case 'bullish': return '📈';
      case 'bearish': return '📉';
      default: return '➖';
    }
  };

  const impact = getImpactLevel(article.relevance_score);
  const timeAgo = new Date() - new Date(article.published_at);
  const hoursAgo = Math.floor(timeAgo / (1000 * 60 * 60));
  const minutesAgo = Math.floor(timeAgo / (1000 * 60));
  
  const displayTime = hoursAgo > 0 ? `${hoursAgo} hour${hoursAgo > 1 ? 's' : ''} ago` : `${minutesAgo} minutes ago`;

  return (
    <div className="flex items-start space-x-3 p-3 hover:bg-gray-50 rounded-lg transition-colors group cursor-pointer">
      <div className="text-lg mt-1">
        {getSentimentIcon(article.sentiment)}
      </div>
      <div className="flex-1 min-w-0">
        <h3 className="text-sm font-semibold text-gray-900 group-hover:text-blue-600 line-clamp-2">
          {article.title}
        </h3>
        <div className="flex items-center space-x-4 mt-1 text-xs text-gray-500">
          <span className="font-medium">{article.source}</span>
          <span>{displayTime}</span>
          <Badge className={`px-2 py-1 text-xs ${impact.color} border-none`}>
            {impact.label}
          </Badge>
        </div>
        <div className="mt-1 text-xs">
          <span>Sentiment: </span>
          <span className={`font-medium ${
            article.sentiment === 'bullish' ? 'text-green-600' : 
            article.sentiment === 'bearish' ? 'text-red-600' : 'text-gray-600'
          }`}>
            {article.sentiment === 'bullish' ? '+100%' : 
             article.sentiment === 'bearish' ? '-100%' : '0%'}
          </span>
          <span className="text-gray-500"> (confidence: 100%)</span>
        </div>
      </div>
      <ExternalLink className="w-4 h-4 text-gray-400 group-hover:text-blue-600" />
    </div>
  );
};

function App() {
  const [sentimentData, setSentimentData] = useState(null);
  const [historyData, setHistoryData] = useState([]);
  const [newsArticles, setNewsArticles] = useState([]);
  const [loading, setLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState(null);
  const [error, setError] = useState(null);

  const fetchSentimentAnalysis = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await axios.post(`${API_BASE_URL}/api/analyze`);
      setSentimentData(response.data.sentiment_analysis);
      setNewsArticles(response.data.articles);
      setLastUpdated(new Date());
      
      // Fetch updated history
      await fetchHistoryData();
    } catch (error) {
      console.error('Error fetching sentiment analysis:', error);
      setError(error.response?.data?.detail || 'Failed to fetch sentiment analysis');
    } finally {
      setLoading(false);
    }
  };

  const fetchHistoryData = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/sentiment-history`);
      const analyses = response.data.analyses;
      
      // Transform data for 24h trend chart
      const transformedData = analyses.map((analysis, index) => ({
        time: new Date(analysis.timestamp).getHours(),
        sentiment: analysis.overall_sentiment === 'bullish' ? analysis.bullish_score * 10 :
                  analysis.overall_sentiment === 'bearish' ? -analysis.bearish_score * 10 : 0,
        timestamp: analysis.timestamp
      }));
      
      setHistoryData(transformedData);
    } catch (error) {
      console.error('Error fetching history data:', error);
    }
  };

  useEffect(() => {
    fetchHistoryData();
  }, []);

  const formatTime = (date) => {
    return date.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: true 
    });
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Forex Market Sentiment</h1>
            <p className="text-gray-600">Live sentiment analysis with full article content</p>
          </div>
        </div>

        {/* Status Bar */}
        <div className="flex items-center justify-between bg-white rounded-lg p-4 shadow-sm">
          <div className="flex items-center space-x-6">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-sm font-medium text-green-600">Live Data</span>
            </div>
            <div className="flex items-center space-x-2">
              <Activity className="w-4 h-4 text-gray-500" />
              <span className="text-sm text-gray-600">
                Last update: {lastUpdated ? formatTime(lastUpdated) : '--:-- --'}
              </span>
            </div>
            <div className="text-sm text-gray-600">
              <span className="font-medium">{newsArticles.length}</span> articles analyzed
            </div>
          </div>
          <Button
            onClick={fetchSentimentAnalysis}
            disabled={loading}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2"
          >
            {loading ? (
              <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <RefreshCw className="w-4 h-4 mr-2" />
            )}
            Refresh
          </Button>
        </div>

        {/* Error Alert */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <p className="text-red-800 text-sm">{error}</p>
          </div>
        )}

        {/* Main Sentiment Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          
          {/* Overall Sentiment */}
          <Card className="bg-white shadow-sm">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg font-bold">Overall Sentiment</CardTitle>
              <CardDescription className="text-sm text-gray-600">
                Current market sentiment score (Confidence: {sentimentData ? (sentimentData.confidence_score * 100).toFixed(0) : 0}%)
              </CardDescription>
            </CardHeader>
            <CardContent>
              {sentimentData ? (
                <SentimentGauge 
                  sentiment={sentimentData.overall_sentiment} 
                  confidence={sentimentData.confidence_score * 100}
                />
              ) : (
                <div className="h-20 bg-gray-100 rounded-lg animate-pulse"></div>
              )}
            </CardContent>
          </Card>

          {/* Upward Probability */}
          <Card className="bg-white shadow-sm">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg font-bold">Upward Probability</CardTitle>
              <CardDescription className="text-sm text-gray-600">
                Likelihood of market moving up
              </CardDescription>
            </CardHeader>
            <CardContent className="flex justify-center">
              {sentimentData ? (
                <CircularGauge 
                  value={sentimentData.bullish_score} 
                  maxValue={10}
                  color="#10b981"
                  label="Bullish"
                />
              ) : (
                <div className="w-24 h-24 bg-gray-100 rounded-full animate-pulse"></div>
              )}
            </CardContent>
          </Card>

          {/* Downward Probability */}
          <Card className="bg-white shadow-sm">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg font-bold">Downward Probability</CardTitle>
              <CardDescription className="text-sm text-gray-600">
                Likelihood of market moving down
              </CardDescription>
            </CardHeader>
            <CardContent className="flex justify-center">
              {sentimentData ? (
                <CircularGauge 
                  value={sentimentData.bearish_score} 
                  maxValue={10}
                  color="#ef4444"
                  label="Bearish"
                />
              ) : (
                <div className="w-24 h-24 bg-gray-100 rounded-full animate-pulse"></div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Bottom Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          
          {/* Sentiment Trend Chart */}
          <Card className="bg-white shadow-sm">
            <CardHeader>
              <CardTitle className="text-lg font-bold">Sentiment Trend (24h)</CardTitle>
              <CardDescription className="text-sm text-gray-600">
                Hourly sentiment analysis from live news feeds
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={historyData}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                    <XAxis 
                      dataKey="time" 
                      tickFormatter={(hour) => `${hour}:00`}
                      stroke="#6b7280"
                      fontSize={12}
                    />
                    <YAxis 
                      domain={[-100, 100]}
                      tickFormatter={(value) => value.toString()}
                      stroke="#6b7280"
                      fontSize={12}
                    />
                    <Tooltip 
                      labelFormatter={(hour) => `${hour}:00`}
                      formatter={(value) => [value, 'Sentiment Score']}
                      contentStyle={{
                        backgroundColor: 'white',
                        border: '1px solid #e5e7eb',
                        borderRadius: '8px',
                        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                      }}
                    />
                    <Line 
                      type="monotone" 
                      dataKey="sentiment" 
                      stroke="#10b981" 
                      strokeWidth={2}
                      dot={{ fill: '#10b981', strokeWidth: 2, r: 3 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          {/* Live News Analysis */}
          <Card className="bg-white shadow-sm">
            <CardHeader>
              <CardTitle className="text-lg font-bold">Live News Analysis</CardTitle>
              <CardDescription className="text-sm text-gray-600">
                Click any headline to read the full article
              </CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              <div className="max-h-64 overflow-y-auto">
                {newsArticles.length > 0 ? (
                  <div className="space-y-1">
                    {newsArticles.slice(0, 5).map((article, index) => (
                      <NewsArticle key={article.id} article={article} index={index} />
                    ))}
                  </div>
                ) : (
                  <div className="p-6 text-center text-gray-500">
                    <p>No news articles available</p>
                    <p className="text-sm mt-1">Click "Refresh" to fetch latest news</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

      </div>
    </div>
  );
}

export default App;